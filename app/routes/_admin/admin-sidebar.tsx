import { NavLink, useFetcher, useLocation } from 'react-router'
import LogoutIcon from '~/components/icons/logout-icon'
import { Button } from '~/components/ui/button'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '~/components/ui/sidebar'
import useLogout from '~/hooks/use-logout'

const menu = [
  {
    title: 'Add Spotlight',
    url: '/admin/add-spotlight',
  },
  {
    title: 'Browse Spotlight',
    url: '/admin/browse-spotlight',
  },
  {
    title: 'Add Document',
    url: '/admin/add-document',
  },
  {
    title: 'Browse Documents',
    url: '/admin/browse-documents',
  },
  {
    title: 'Categories',
    url: '/admin/categories',
  },
]

export default function AdminSidebar() {
  const fetcher = useFetcher()
  const location = useLocation()
  const { logout } = useLogout()

  const handleLogout = () => {
    logout.mutate(undefined, {
      onError: () => {
        void fetcher.submit(null, {
          action: '/logout',
          method: 'POST',
        })
      },
      onSuccess: () => {
        void fetcher.submit(null, {
          action: '/logout',
          method: 'POST',
        })
      },
    })
  }

  return (
    <Sidebar>
      <SidebarContent className="flex flex-col">
        <SidebarGroup className="grow">
          <SidebarGroupContent>
            <SidebarMenu>
              {menu.map(item => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={item.url === location.pathname}
                  >
                    <NavLink to={item.url}>{item.title}</NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarMenu>
            <SidebarMenuItem>
              <Button
                onClick={handleLogout}
                isLoading={logout.isPending}
                variant="destructive"
                className="w-full"
              >
                <LogoutIcon className="mr-1" />
                Log Out
              </Button>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
